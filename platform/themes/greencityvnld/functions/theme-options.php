<?php

use <PERSON><PERSON><PERSON>\Theme\Events\RenderingThemeOptionSettings;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\MediaImageField;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\RepeaterField;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\TextareaField;
use Bo<PERSON>ble\Theme\ThemeOption\Fields\TextField;

app('events')->listen(RenderingThemeOptionSettings::class, function (): void {

    // ==================== 1. CÀI ĐẶT CHUNG ====================
    theme_option()
        ->setSection([
            'id' => 'opt-general',
            'title' => '1. Cài đặt chung',
            'icon' => 'ti ti-settings',
        ])
        ->setField([
            'id' => 'site_title',
            'section_id' => 'opt-general',
            'type' => 'text',
            'label' => 'Tiêu đề website',
            'attributes' => [
                'name' => 'site_title',
                'value' => 'Green City Bình Dương',
                'options' => ['class' => 'form-control'],
            ],
        ])
        ->setField([
            'id' => 'contact_phone',
            'section_id' => 'opt-general',
            'type' => 'text',
            'label' => 'Số điện thoại liên hệ',
            'attributes' => [
                'name' => 'contact_phone',
                'value' => '0708808891',
                'options' => ['class' => 'form-control'],
            ],
        ])
        ->setField([
            'id' => 'primary_color',
            'section_id' => 'opt-general',
            'type' => 'customColor',
            'label' => 'Màu chính',
            'attributes' => [
                'name' => 'primary_color',
                'value' => '#01488f',
            ],
        ])
        ->setField([
            'id' => 'zalo_phone',
            'section_id' => 'opt-general',
            'type' => 'text',
            'label' => 'Số điện thoại Zalo',
            'attributes' => ['name' => 'zalo_phone', 'value' => '0708808891', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'logo_alt_text',
            'section_id' => 'opt-general',
            'type' => 'text',
            'label' => 'Alt text logo',
            'attributes' => ['name' => 'logo_alt_text', 'value' => 'GREEN CITY BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 2. BANNER & NAVIGATION ====================
    theme_option()
        ->setSection([
            'id' => 'opt-banner',
            'title' => '2. Banner & Navigation',
            'icon' => 'ti ti-photo',
        ])
        ->setField([
            'id' => 'hero_video_url',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'URL Video YouTube Hero',
            'attributes' => [
                'name' => 'hero_video_url',
                'value' => 'https://www.youtube.com/embed/Y_Mewkp3RvY?autoplay=1&mute=1&loop=1&playlist=Y_Mewkp3RvY&controls=0&showinfo=0&modestbranding=1&fs=0&cc_load_policy=0&iv_load_policy=3&autohide=0&rel=0',
                'options' => ['class' => 'form-control'],
            ],
        ])
        ->setField([
            'id' => 'nav_tong_quan',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Tổng Quan',
            'attributes' => ['name' => 'nav_tong_quan', 'value' => 'Tổng Quan', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_vi_tri',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Vị Trí',
            'attributes' => ['name' => 'nav_vi_tri', 'value' => 'Vị Trí', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_tien_ich',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Tiện Ích',
            'attributes' => ['name' => 'nav_tien_ich', 'value' => 'Tiện Ích', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_mat_bang',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Mặt Bằng',
            'attributes' => ['name' => 'nav_mat_bang', 'value' => 'Mặt Bằng', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_thanh_toan',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Thanh Toán',
            'attributes' => ['name' => 'nav_thanh_toan', 'value' => 'Thanh Toán', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_hinh_anh',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Hình Ảnh',
            'attributes' => ['name' => 'nav_hinh_anh', 'value' => 'Hình Ảnh', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_video',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Video',
            'attributes' => ['name' => 'nav_video', 'value' => 'Video', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'nav_lien_he',
            'section_id' => 'opt-banner',
            'type' => 'text',
            'label' => 'Menu: Liên Hệ',
            'attributes' => ['name' => 'nav_lien_he', 'value' => 'Liên Hệ', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 3. TỔNG QUAN DỰ ÁN ====================
    theme_option()
        ->setSection([
            'id' => 'opt-project-overview',
            'title' => '3. Tổng quan dự án',
            'icon' => 'ti ti-info-circle',
        ])
        ->setField([
            'id' => 'tong_quan_title',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Tiêu đề tổng quan',
            'attributes' => ['name' => 'tong_quan_title', 'value' => 'TỔNG QUAN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tong_quan_title',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Tiêu đề section',
            'attributes' => ['name' => 'tong_quan_title', 'value' => 'TỔNG QUAN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_name',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Tên dự án',
            'attributes' => ['name' => 'project_name', 'value' => 'Green City Bình Dương', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_developer',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Chủ đầu tư',
            'attributes' => ['name' => 'project_developer', 'value' => 'Becamex IDC', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_location',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Vị trí',
            'attributes' => ['name' => 'project_location', 'value' => 'Phường Bình Dương, Tp Hồ Chí Minh', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_area_units',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Diện tích & Số lượng',
            'attributes' => ['name' => 'project_area_units', 'value' => '20 ha gần 1400 căn', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_product_types',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Loại hình sản phẩm',
            'attributes' => ['name' => 'project_product_types', 'value' => 'Shophouse, Nhà Phố Liền Kề', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_building_standards',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Quy chuẩn xây dựng',
            'attributes' => ['name' => 'project_building_standards', 'value' => '1 Trệt – 1 Lửng – 2 Lầu (110m² – 150m²)', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_handover_time',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Thời gian bàn giao',
            'attributes' => ['name' => 'project_handover_time', 'value' => 'Dự kiến năm 2026', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'project_bank_support',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Ngân hàng hỗ trợ',
            'attributes' => ['name' => 'project_bank_support', 'value' => 'BIDV', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tong_quan_label_ten_du_an',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Label tên dự án',
            'attributes' => ['name' => 'tong_quan_label_ten_du_an', 'value' => 'Tên dự án:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tong_quan_label_chu_dau_tu',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Label chủ đầu tư',
            'attributes' => ['name' => 'tong_quan_label_chu_dau_tu', 'value' => 'Chủ đầu tư:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tong_quan_label_vi_tri',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Label vị trí',
            'attributes' => ['name' => 'tong_quan_label_vi_tri', 'value' => 'Vị trí:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tong_quan_label_dien_tich',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Label diện tích',
            'attributes' => ['name' => 'tong_quan_label_dien_tich', 'value' => 'Diện tích & Số lượng sản phẩm:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tong_quan_label_loai_hinh',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Label loại hình',
            'attributes' => ['name' => 'tong_quan_label_loai_hinh', 'value' => 'Loại hình sản phẩm:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tong_quan_label_quy_chuan',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Label quy chuẩn',
            'attributes' => ['name' => 'tong_quan_label_quy_chuan', 'value' => 'Quy chuẩn xây dựng:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tong_quan_label_thoi_gian',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Label thời gian',
            'attributes' => ['name' => 'tong_quan_label_thoi_gian', 'value' => 'Thời gian bàn giao:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tong_quan_label_ngan_hang',
            'section_id' => 'opt-project-overview',
            'type' => 'text',
            'label' => 'Label ngân hàng',
            'attributes' => ['name' => 'tong_quan_label_ngan_hang', 'value' => 'Ngân hàng hỗ trợ:', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 4. VỊ TRÍ & LIÊN KẾT VÙNG ====================
    theme_option()
        ->setSection([
            'id' => 'opt-location',
            'title' => '4. Vị trí & Liên kết vùng',
            'icon' => 'ti ti-map-pin',
        ])
        ->setField([
            'id' => 'vi_tri_title',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề vị trí',
            'attributes' => ['name' => 'vi_tri_title', 'value' => 'VỊ TRÍ GREEN CITY BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'vi_tri_subtitle',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Phụ đề vị trí',
            'attributes' => ['name' => 'vi_tri_subtitle', 'value' => 'THÀNH PHỐ MỚI BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'vi_tri_content_title_1',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề nội dung 1',
            'attributes' => ['name' => 'vi_tri_content_title_1', 'value' => 'Green City Bình Dương – Đòn bẩy tăng trưởng giữa trung tâm kinh tế trọng điểm', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'vi_tri_content_desc_1',
            'section_id' => 'opt-location',
            'type' => 'textarea',
            'label' => 'Mô tả nội dung 1',
            'attributes' => ['name' => 'vi_tri_content_desc_1', 'value' => 'Green City Bình Dương tọa lạc ngay trung tâm Thành phố mới Bình Dương ngay góc đường huyết mạch Đại lộ Lê Lợi, Võ Nguyên Giáp. Dự án nằm trong vùng quy hoạch chiến lược, kết nối trực tiếp đến Trung tâm Hành Chính, KCN Mỹ Phước, KCN Vsip II, Quốc lộ 13, mở ra cơ hội sinh lời vượt trội.', 'options' => ['class' => 'form-control', 'rows' => 3]],
        ])
        ->setField([
            'id' => 'vi_tri_content_title_2',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề nội dung 2',
            'attributes' => ['name' => 'vi_tri_content_title_2', 'value' => 'Green City Bình Dương – Nơi khởi đầu cuộc sống xanh giữa lòng đô thị mới', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'vi_tri_content_desc_2',
            'section_id' => 'opt-location',
            'type' => 'textarea',
            'label' => 'Mô tả nội dung 2',
            'attributes' => ['name' => 'vi_tri_content_desc_2', 'value' => 'Dự án sở hữu môi trường sống trong lành, quy hoạch hiện đại, tiện ích đồng bộ – là nơi lý tưởng để xây dựng tổ ấm, an cư lâu dài giữa vùng đô thị phát triển năng động bậc nhất miền Nam.', 'options' => ['class' => 'form-control', 'rows' => 3]],
        ])
        ->setField([
            'id' => 'vi_tri_image',
            'section_id' => 'opt-location',
            'type' => 'mediaImage',
            'label' => 'Hình ảnh vị trí',
            'attributes' => ['name' => 'vi_tri_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'lien_ket_vung_title',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề liên kết vùng',
            'attributes' => ['name' => 'lien_ket_vung_title', 'value' => 'LIÊN KẾT VÙNG GREEN CITY BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_ket_vung_subtitle',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Phụ đề liên kết vùng',
            'attributes' => ['name' => 'lien_ket_vung_subtitle', 'value' => 'Liên kết vùng & Tiềm năng phát triển của Green City Bình Dương', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_ket_vung_content_title',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề nội dung liên kết vùng',
            'attributes' => ['name' => 'lien_ket_vung_content_title', 'value' => 'Liên kết Vùng chiến lược của Green City Bình Dương', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_ket_vung_content_desc',
            'section_id' => 'opt-location',
            'type' => 'textarea',
            'label' => 'Mô tả nội dung liên kết vùng',
            'attributes' => ['name' => 'lien_ket_vung_content_desc', 'value' => 'Green City Bình Dương nằm trong lòng Trung tâm Thành phố mới Bình Dương – khu vực đầu tàu quy hoạch theo mô hình đô thị thông minh, hiện đại và đa chức năng. Đây chính là trung tâm hành chính, tài chính, giáo dục và công nghệ cao của tỉnh Bình Dương.', 'options' => ['class' => 'form-control', 'rows' => 3]],
        ])
        ->setField([
            'id' => 'lien_ket_vung_map_image',
            'section_id' => 'opt-location',
            'type' => 'mediaImage',
            'label' => 'Hình ảnh bản đồ liên kết vùng',
            'attributes' => ['name' => 'lien_ket_vung_map_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_1_title',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề item 1',
            'attributes' => ['name' => 'lien_ket_vung_item_1_title', 'value' => 'Nằm trên trục Đại lộ Bình Dương', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_1_desc',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Mô tả item 1',
            'attributes' => ['name' => 'lien_ket_vung_item_1_desc', 'value' => 'tuyến đường xương sống kết nối từ trung tâm TP.HCM đến phía bắc tỉnh Bình Dương.', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_2_title',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề item 2',
            'attributes' => ['name' => 'lien_ket_vung_item_2_title', 'value' => 'Giao cắt với Võ Nguyên Giáp và Đại lộ Lê Lợi', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_2_desc',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Mô tả item 2',
            'attributes' => ['name' => 'lien_ket_vung_item_2_desc', 'value' => 'tạo mạng lưới giao thông đa chiều trong đô thị. Kết nối Đông Tây với việc kết nối DT741 và DT742.', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_3_title',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề item 3',
            'attributes' => ['name' => 'lien_ket_vung_item_3_title', 'value' => 'Liền kề Vành đai 4', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_ket_vung_item_3_desc',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Mô tả item 3',
            'attributes' => ['name' => 'lien_ket_vung_item_3_desc', 'value' => 'giúp kết nối TP.HCM – Đồng Nai – Tây Ninh, tạo thành trục phát triển liên vùng nhanh chóng và hiệu quả.', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'vi_tri_lien_ket_title',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Tiêu đề vị trí liên kết',
            'attributes' => ['name' => 'vi_tri_lien_ket_title', 'value' => 'Vị trí liên kết vùng', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'vi_tri_lien_ket_desc',
            'section_id' => 'opt-location',
            'type' => 'text',
            'label' => 'Mô tả vị trí liên kết',
            'attributes' => ['name' => 'vi_tri_lien_ket_desc', 'value' => 'Sơ đồ mạng lưới giao thông và kết nối chiến lược của Green City Bình Dương', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 5. TIỆN ÍCH ====================
    theme_option()
        ->setSection([
            'id' => 'opt-amenities',
            'title' => '5. Tiện ích',
            'icon' => 'ti ti-building-community',
        ])
        ->setField([
            'id' => 'tien_ich_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiêu đề tiện ích',
            'attributes' => ['name' => 'tien_ich_title', 'value' => 'TIỆN ÍCH GREEN CITY BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiêu đề tiện ích nội khu',
            'attributes' => ['name' => 'tien_ich_noi_khu_title', 'value' => 'Tiện ích nội khu:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiêu đề tiện ích ngoại khu',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_title', 'value' => 'Tiện ích ngoại khu vượt trội:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_desc',
            'section_id' => 'opt-amenities',
            'type' => 'textarea',
            'label' => 'Mô tả tiện ích ngoại khu',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_desc', 'value' => 'Nằm ngay vị trí lõi thành phố mới Bình Dương, Green City thừa hưởng trọn vẹn hệ thống tiện ích hiện đại của khu vực:', 'options' => ['class' => 'form-control', 'rows' => 3]],
        ])
        // Tiện ích nội khu 1-5
        ->setField([
            'id' => 'tien_ich_noi_khu_1_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 1 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_noi_khu_1_title', 'value' => 'Green School', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_1_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 1 - Mô tả',
            'attributes' => ['name' => 'tien_ich_noi_khu_1_desc', 'value' => 'Trường mầm non với thương hiệu Green School', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_1_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích nội khu 1 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_noi_khu_1_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_2_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 2 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_noi_khu_2_title', 'value' => 'Green Mall', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_2_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 2 - Mô tả',
            'attributes' => ['name' => 'tien_ich_noi_khu_2_desc', 'value' => 'Trung tâm thương mại đẳng cấp nội khu', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_2_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích nội khu 2 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_noi_khu_2_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_3_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 3 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_noi_khu_3_title', 'value' => 'Green Garden', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_3_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 3 - Mô tả',
            'attributes' => ['name' => 'tien_ich_noi_khu_3_desc', 'value' => 'Công viên và vườn đi bộ cao cấp', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_3_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích nội khu 3 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_noi_khu_3_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_4_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 4 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_noi_khu_4_title', 'value' => 'Không gian sinh hoạt chung', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_4_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 4 - Mô tả',
            'attributes' => ['name' => 'tien_ich_noi_khu_4_desc', 'value' => 'Khu vực sinh hoạt cộng đồng hiện đại', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_4_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích nội khu 4 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_noi_khu_4_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_5_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 5 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_noi_khu_5_title', 'value' => 'Green Gym', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_5_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích nội khu 5 - Mô tả',
            'attributes' => ['name' => 'tien_ich_noi_khu_5_desc', 'value' => 'Phòng tập Gym cao cấp thương hiệu Green Gym', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_noi_khu_5_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích nội khu 5 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_noi_khu_5_image', 'value' => ''],
        ])
        // Tiện ích ngoại khu 1-6
        ->setField([
            'id' => 'tien_ich_ngoai_khu_1_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 1 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_1_title', 'value' => 'TTHC Bình Dương', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_1_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 1 - Mô tả',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_1_desc', 'value' => 'Trung tâm Hành chính tỉnh Bình Dương', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_1_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích ngoại khu 1 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_1_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_2_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 2 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_2_title', 'value' => 'KCN VSIP II', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_2_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 2 - Mô tả',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_2_desc', 'value' => 'Khu Công nghiệp VSIP II và các tập đoàn đa quốc gia', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_2_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích ngoại khu 2 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_2_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_3_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 3 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_3_title', 'value' => 'Đại học Quốc tế Miền Đông', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_3_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 3 - Mô tả',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_3_desc', 'value' => 'Mạng lưới trường học liên cấp chuẩn quốc tế', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_3_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích ngoại khu 3 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_3_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_4_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 4 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_4_title', 'value' => 'Bệnh viện 1500 giường', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_4_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 4 - Mô tả',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_4_desc', 'value' => 'Hệ thống y tế cao cấp và hiện đại', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_4_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích ngoại khu 4 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_4_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_5_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 5 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_5_title', 'value' => 'AEON Mall', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_5_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 5 - Mô tả',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_5_desc', 'value' => 'Trung tâm thương mại và siêu thị hiện đại', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_5_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích ngoại khu 5 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_5_image', 'value' => ''],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_6_title',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 6 - Tiêu đề',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_6_title', 'value' => 'Quốc lộ 13', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_6_desc',
            'section_id' => 'opt-amenities',
            'type' => 'text',
            'label' => 'Tiện ích ngoại khu 6 - Mô tả',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_6_desc', 'value' => 'Kết nối các trục giao thông huyết mạch', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'tien_ich_ngoai_khu_6_image',
            'section_id' => 'opt-amenities',
            'type' => 'mediaImage',
            'label' => 'Tiện ích ngoại khu 6 - Hình ảnh',
            'attributes' => ['name' => 'tien_ich_ngoai_khu_6_image', 'value' => ''],
        ]);

    // ==================== 6. CHỦ ĐẦU TƯ ====================
    theme_option()
        ->setSection([
            'id' => 'opt-developer',
            'title' => '6. Chủ đầu tư',
            'icon' => 'ti ti-building-skyscraper',
        ])
        ->setField([
            'id' => 'chu_dau_tu_title',
            'section_id' => 'opt-developer',
            'type' => 'text',
            'label' => 'Tiêu đề',
            'attributes' => ['name' => 'chu_dau_tu_title', 'value' => 'CHỦ ĐẦU TƯ UY TÍN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'chu_dau_tu_subtitle',
            'section_id' => 'opt-developer',
            'type' => 'text',
            'label' => 'Phụ đề',
            'attributes' => ['name' => 'chu_dau_tu_subtitle', 'value' => 'BECAMEX IDC - TẬP ĐOÀN HÀNG ĐẦU VIỆT NAM', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'chu_dau_tu_logo',
            'section_id' => 'opt-developer',
            'type' => 'mediaImage',
            'label' => 'Logo chủ đầu tư',
            'attributes' => ['name' => 'chu_dau_tu_logo', 'value' => ''],
        ])
        ->setField([
            'id' => 'chu_dau_tu_name',
            'section_id' => 'opt-developer',
            'type' => 'text',
            'label' => 'Tên chủ đầu tư',
            'attributes' => ['name' => 'chu_dau_tu_name', 'value' => 'Tổng Công ty Đầu tư và Phát triển Công nghiệp (Becamex IDC)', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'chu_dau_tu_description',
            'section_id' => 'opt-developer',
            'type' => 'textarea',
            'label' => 'Mô tả chủ đầu tư',
            'attributes' => ['name' => 'chu_dau_tu_description', 'value' => 'Đơn vị phát triển hạ tầng và đô thị hàng đầu Việt Nam với hơn 30 năm kinh nghiệm. Becamex IDC đã thành công phát triển nhiều khu đô thị, khu công nghiệp quy mô lớn tại Bình Dương và các tỉnh thành khác.', 'options' => ['class' => 'form-control', 'rows' => 3]],
        ])
        ->setField([
            'id' => 'developer_highlight_1',
            'section_id' => 'opt-developer',
            'type' => 'text',
            'label' => 'Điểm nổi bật 1',
            'attributes' => ['name' => 'developer_highlight_1', 'value' => 'Hơn 30 năm kinh nghiệm phát triển đô thị', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'developer_highlight_2',
            'section_id' => 'opt-developer',
            'type' => 'text',
            'label' => 'Điểm nổi bật 2',
            'attributes' => ['name' => 'developer_highlight_2', 'value' => 'Tập đoàn bất động sản uy tín hàng đầu', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'developer_highlight_3',
            'section_id' => 'opt-developer',
            'type' => 'text',
            'label' => 'Điểm nổi bật 3',
            'attributes' => ['name' => 'developer_highlight_3', 'value' => 'Đối tác tin cậy của nhiều tập đoàn quốc tế', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 7. MẶT BẰNG ====================
    theme_option()
        ->setSection([
            'id' => 'opt-masterplan',
            'title' => '7. Mặt bằng',
            'icon' => 'ti ti-layout-grid',
        ])
        ->setField([
            'id' => 'mat_bang_title',
            'section_id' => 'opt-masterplan',
            'type' => 'text',
            'label' => 'Tiêu đề',
            'attributes' => ['name' => 'mat_bang_title', 'value' => 'MẶT BẰNG DỰ ÁN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'mat_bang_image',
            'section_id' => 'opt-masterplan',
            'type' => 'mediaImage',
            'label' => 'Hình ảnh mặt bằng',
            'attributes' => ['name' => 'mat_bang_image', 'value' => null],
        ]);

    // ==================== 8. THANH TOÁN ====================
    theme_option()
        ->setSection([
            'id' => 'opt-payment',
            'title' => '8. Thanh toán',
            'icon' => 'ti ti-credit-card',
        ])
        ->setField([
            'id' => 'thanh_toan_title',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Tiêu đề',
            'attributes' => ['name' => 'thanh_toan_title', 'value' => 'PHƯƠNG THỨC THANH TOÁN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'loan_interest_rate',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Lãi suất vay',
            'attributes' => ['name' => 'loan_interest_rate', 'value' => 'dự kiến 6,5%/năm', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'loan_support_ratio',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Tỷ lệ hỗ trợ',
            'attributes' => ['name' => 'loan_support_ratio', 'value' => 'tối đa 70% giá trị Hợp đồng mua bán', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'loan_duration',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Thời gian vay',
            'attributes' => ['name' => 'loan_duration', 'value' => 'tối đa 30 năm', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'payment_loan_info_title',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Tiêu đề thông tin gói vay',
            'attributes' => ['name' => 'payment_loan_info_title', 'value' => 'THÔNG TIN GÓI VAY', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'payment_loan_interest_label',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Label lãi suất ưu đãi',
            'attributes' => ['name' => 'payment_loan_interest_label', 'value' => 'Khách hàng được ưu đãi lãi suất:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'payment_loan_duration_label',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Label thời gian áp dụng',
            'attributes' => ['name' => 'payment_loan_duration_label', 'value' => 'Thời gian áp dụng: đến khi BIDV có văn bản thông báo dừng gói tín dụng', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'payment_support_ratio_label',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Label tỷ lệ tài trợ',
            'attributes' => ['name' => 'payment_support_ratio_label', 'value' => 'Tỷ lệ tài trợ:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'payment_loan_duration_value_label',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Label thời gian vay',
            'attributes' => ['name' => 'payment_loan_duration_value_label', 'value' => 'Thời gian vay:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'price_table_title',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Tiêu đề bảng giá',
            'attributes' => ['name' => 'price_table_title', 'value' => 'BẢNG GIÁ BÁN GREEN CITY BÌNH DƯƠNG', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_header_loai_sp',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Header loại sản phẩm',
            'attributes' => ['name' => 'bang_gia_header_loai_sp', 'value' => 'LOẠI SẢN PHẨM', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_header_dien_tich',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Header diện tích',
            'attributes' => ['name' => 'bang_gia_header_dien_tich', 'value' => 'DIỆN TÍCH M²', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_header_gia_thap',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Header giá thấp nhất',
            'attributes' => ['name' => 'bang_gia_header_gia_thap', 'value' => 'GIÁ THẤP NHẤT (TỶ ĐỒNG)', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_header_gia_cao',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Header giá cao nhất',
            'attributes' => ['name' => 'bang_gia_header_gia_cao', 'value' => 'GIÁ CAO NHẤT (TỶ ĐỒNG)', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_lien_ke_title',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Tên sản phẩm liền kề',
            'attributes' => ['name' => 'bang_gia_lien_ke_title', 'value' => 'Nhà liền kề', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_shophouse_title',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Tên sản phẩm shophouse',
            'attributes' => ['name' => 'bang_gia_shophouse_title', 'value' => 'Shophouse', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_cap_nhat_text',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Text cập nhật giá',
            'attributes' => ['name' => 'bang_gia_cap_nhat_text', 'value' => 'Cập nhật', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_label_dien_tich',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Label diện tích mobile',
            'attributes' => ['name' => 'bang_gia_label_dien_tich', 'value' => 'Diện tích:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_label_gia_thap',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Label giá thấp mobile',
            'attributes' => ['name' => 'bang_gia_label_gia_thap', 'value' => 'Giá thấp nhất:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_label_gia_cao',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Label giá cao mobile',
            'attributes' => ['name' => 'bang_gia_label_gia_cao', 'value' => 'Giá cao nhất:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'bang_gia_luu_y_label',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Label lưu ý',
            'attributes' => ['name' => 'bang_gia_luu_y_label', 'value' => 'Lưu ý:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'price_note',
            'section_id' => 'opt-payment',
            'type' => 'textarea',
            'label' => 'Ghi chú giá',
            'attributes' => ['name' => 'price_note', 'value' => 'Giá bán liền kề, shophouse Green City trung bình khoảng —, bảng giá Green City Bình Dương sẽ được chủ đầu tư Becamex IDC công bố đồng loạt từng lô trong ngày mở bán.', 'options' => ['class' => 'form-control', 'rows' => 3]],
        ])
        ->setField([
            'id' => 'contact_cta_button_text',
            'section_id' => 'opt-payment',
            'type' => 'text',
            'label' => 'Text button CTA',
            'attributes' => ['name' => 'contact_cta_button_text', 'value' => 'TƯ VẤN & BÁO GIÁ: 0708808891', 'options' => ['class' => 'form-control']],
        ]);

    // ==================== 9. HÌNH ẢNH & VIDEO ====================
    theme_option()
        ->setSection([
            'id' => 'opt-media',
            'title' => '9. Hình ảnh & Video',
            'icon' => 'ti ti-photo-circle',
        ])
        ->setField([
            'id' => 'gallery_title',
            'section_id' => 'opt-media',
            'type' => 'text',
            'label' => 'Tiêu đề gallery',
            'attributes' => ['name' => 'gallery_title', 'value' => 'HÌNH ẢNH DỰ ÁN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'video_title',
            'section_id' => 'opt-media',
            'type' => 'text',
            'label' => 'Tiêu đề video',
            'attributes' => ['name' => 'video_title', 'value' => 'VIDEO GIỚI THIỆU', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'video_embed_url',
            'section_id' => 'opt-media',
            'type' => 'text',
            'label' => 'URL Video YouTube Embed',
            'attributes' => ['name' => 'video_embed_url', 'value' => 'https://www.youtube.com/embed/9po0pfbZRNM?enablejsapi=1&origin=https://greencity-binhduong.com', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'carousel_previous_text',
            'section_id' => 'opt-media',
            'type' => 'text',
            'label' => 'Text nút Previous',
            'attributes' => ['name' => 'carousel_previous_text', 'value' => 'Previous', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'carousel_next_text',
            'section_id' => 'opt-media',
            'type' => 'text',
            'label' => 'Text nút Next',
            'attributes' => ['name' => 'carousel_next_text', 'value' => 'Next', 'options' => ['class' => 'form-control']],
        ])
        ->setField(
            RepeaterField::make()
                ->id('gallery_slides')
                ->sectionId('opt-media')
                ->name('gallery_slides')
                ->label('Slides hình ảnh')
                ->defaultValue([])
                ->fields([
                    MediaImageField::make()->name('image')->label('Hình ảnh'),
                    TextField::make()->name('title')->label('Tiêu đề'),
                    TextareaField::make()->name('description')->label('Mô tả')->rows(2),
                ])
                ->toArray()
        );

    // ==================== 10. LIÊN HỆ & FOOTER ====================
    theme_option()
        ->setSection([
            'id' => 'opt-contact',
            'title' => '10. Liên hệ & Footer',
            'icon' => 'ti ti-phone',
        ])
        ->setField([
            'id' => 'lien_he_title',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tiêu đề liên hệ',
            'attributes' => ['name' => 'lien_he_title', 'value' => 'LIÊN HỆ & ĐĂNG KÝ', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'lien_he_subtitle',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Phụ đề liên hệ',
            'attributes' => ['name' => 'lien_he_subtitle', 'value' => 'Đăng ký nhận thông tin và báo giá mới nhất', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'form_name_placeholder',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Placeholder tên',
            'attributes' => ['name' => 'form_name_placeholder', 'value' => 'Họ và tên *', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'form_phone_placeholder',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Placeholder điện thoại',
            'attributes' => ['name' => 'form_phone_placeholder', 'value' => 'Số điện thoại *', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'form_email_placeholder',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Placeholder email',
            'attributes' => ['name' => 'form_email_placeholder', 'value' => 'Email (tùy chọn)', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'form_product_select_label',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Label chọn sản phẩm',
            'attributes' => ['name' => 'form_product_select_label', 'value' => 'Loại sản phẩm quan tâm:', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'form_product_select_placeholder',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Placeholder chọn sản phẩm',
            'attributes' => ['name' => 'form_product_select_placeholder', 'value' => 'Chọn loại sản phẩm', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'product_option_1',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tùy chọn sản phẩm 1',
            'attributes' => ['name' => 'product_option_1', 'value' => 'Liền kề', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'product_option_2',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tùy chọn sản phẩm 2',
            'attributes' => ['name' => 'product_option_2', 'value' => 'Shophouse', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'product_option_3',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tùy chọn sản phẩm 3',
            'attributes' => ['name' => 'product_option_3', 'value' => 'Tư vấn tổng quan', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'form_submit_text',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Text nút gửi',
            'attributes' => ['name' => 'form_submit_text', 'value' => 'ĐĂNG KÝ NHẬN THÔNG TIN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'footer_logo_1',
            'section_id' => 'opt-contact',
            'type' => 'mediaImage',
            'label' => 'Logo Footer 1',
            'attributes' => ['name' => 'footer_logo_1', 'value' => ''],
        ])
        ->setField([
            'id' => 'footer_logo_2',
            'section_id' => 'opt-contact',
            'type' => 'mediaImage',
            'label' => 'Logo Footer 2',
            'attributes' => ['name' => 'footer_logo_2', 'value' => ''],
        ])
        ->setField([
            'id' => 'footer_title',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tiêu đề CTA footer',
            'attributes' => ['name' => 'footer_title', 'value' => 'BOOKING NGAY HÔM NAY - NHẬN NGAY ƯU TIÊN VỊ TRÍ.', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'footer_address',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Địa chỉ',
            'attributes' => ['name' => 'footer_address', 'value' => 'Thành phố mới Bình Dương, tỉnh Bình Dương', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'footer_copyright',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Bản quyền',
            'attributes' => ['name' => 'footer_copyright', 'value' => 'Green City BD. Bản quyền của Công ty TNHH Đầu Tư và Phát Triển Đô Thị Xanh BD.', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'footer_description',
            'section_id' => 'opt-contact',
            'type' => 'textarea',
            'label' => 'Mô tả footer',
            'attributes' => ['name' => 'footer_description', 'value' => 'Green City Bình Dương - Khu đô thị chất lượng cao với môi trường sống tiện nghi hiện đại và không gian xanh đẳng cấp Singapore.', 'options' => ['class' => 'form-control', 'rows' => 3]],
        ])
        ->setField([
            'id' => 'floating_call_text',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Text nút gọi nổi',
            'attributes' => ['name' => 'floating_call_text', 'value' => 'Gọi ngay', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'zalo_text',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Text nút Zalo',
            'attributes' => ['name' => 'zalo_text', 'value' => 'Chat Zalo', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'modal_contact_title',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tiêu đề modal liên hệ',
            'attributes' => ['name' => 'modal_contact_title', 'value' => 'ĐĂNG KÝ NHẬN THÔNG TIN', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'success_popup_title',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tiêu đề popup thành công',
            'attributes' => ['name' => 'success_popup_title', 'value' => 'Cảm ơn bạn!', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'success_popup_message',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Thông báo thành công',
            'attributes' => ['name' => 'success_popup_message', 'value' => 'Thông tin đã được gửi thành công.', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'success_popup_sub_message',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Thông báo phụ thành công',
            'attributes' => ['name' => 'success_popup_sub_message', 'value' => 'Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'error_popup_title',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Tiêu đề popup lỗi',
            'attributes' => ['name' => 'error_popup_title', 'value' => 'Có lỗi xảy ra!', 'options' => ['class' => 'form-control']],
        ])
        ->setField([
            'id' => 'popup_close_text',
            'section_id' => 'opt-contact',
            'type' => 'text',
            'label' => 'Text nút đóng popup',
            'attributes' => ['name' => 'popup_close_text', 'value' => 'Đóng', 'options' => ['class' => 'form-control']],
        ]);
});
